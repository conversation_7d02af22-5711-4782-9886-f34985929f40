# 使用目标感知CDM的训练命令（推荐）

ml-1m (目标感知CDM)
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm True

beauty (目标感知CDM)
python main.py --dataset amazon_beauty --num_cluster 32 --lambda_intent 0.4 --lambda_history 0.6 --lambda_contra 1 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm True

toys (目标感知CDM)
python main.py --dataset toys --num_cluster 32 --lambda_intent 0.4 --lambda_history 1 --lambda_contra 1 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm True

python main.py --dataset toys --num_cluster 32 --lambda_intent 0.4 --lambda_history 1 --lambda_contra 1 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm True

steam (目标感知CDM)
python main.py --dataset steam --num_cluster 32 --lambda_intent 1.2 --lambda_history 1 --lambda_contra 0.2 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm True

# 原始CDM的训练命令（对比用）

ml-1m (原始CDM)
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_target_aware_cdm False
