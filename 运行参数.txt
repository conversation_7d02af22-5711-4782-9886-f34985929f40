# 使用双对比学习的训练命令（推荐）

ml-1m (双对比学习 - 噪声增强)
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive True --lambda_self 0.1 --augment_type noise --noise_scale 0.1 --self_temp 0.1

ml-1m (双对比学习 - dropout增强)
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive True --lambda_self 0.1 --augment_type dropout --dropout_rate 0.15 --self_temp 0.1

beauty (双对比学习)
python main.py --dataset amazon_beauty --num_cluster 32 --lambda_intent 0.4 --lambda_history 0.6 --lambda_contra 1 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive True --lambda_self 0.1 --augment_type noise --noise_scale 0.1 --self_temp 0.1

toys (双对比学习)
python main.py --dataset toys --num_cluster 32 --lambda_intent 0.4 --lambda_history 1 --lambda_contra 1 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive True --lambda_self 0.1 --augment_type mixup --self_temp 0.1

steam (双对比学习)
python main.py --dataset steam --num_cluster 32 --lambda_intent 1.2 --lambda_history 1 --lambda_contra 0.2 --eval_interval 2 --patience 10 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive True --lambda_self 0.1 --augment_type noise --noise_scale 0.1 --self_temp 0.1

# 原始CDM的训练命令（对比用）

ml-1m (原始CDM)
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05 --eval_interval 2 --patience 10 --max_len 200 --use_multi_interest True --num_interests 3 --lambda_multi_interest 0.5 --attention_dropout 0.22 --interest_dropout 0.18 --fusion_dropout 0.16 --use_dual_contrastive False
