"""
目标感知CDM模块的核心逻辑演示
"""

import torch
import torch.nn.functional as F

def original_cdm_loss(generated_items, temperature=0.07):
    """
    原始CDM实现：无差别推开生成物
    """
    similarities = F.cosine_similarity(
        generated_items.unsqueeze(1), 
        generated_items.unsqueeze(0), 
        dim=2
    ) / temperature
    
    # 正样本：自身与自身的相似度
    positives = torch.diag(similarities)
    
    # 负样本：与其他生成物的相似度
    mask = torch.eye(len(generated_items)).bool()
    negatives = similarities[~mask].view(len(generated_items), -1)
    
    # NCE损失
    logits = torch.cat([positives.unsqueeze(1), negatives], dim=1)
    labels = torch.zeros(len(generated_items), dtype=torch.long)
    loss = F.cross_entropy(logits, labels)
    
    return loss

def target_aware_cdm_loss(generated_items, target_items, temperature=0.07):
    """
    目标感知CDM实现：基于真实目标物品的监督对比学习
    """
    batch_size = generated_items.size(0)
    
    # 计算生成物与所有真实目标的相似度矩阵
    similarities = F.cosine_similarity(
        generated_items.unsqueeze(1),  # [batch_size, 1, hidden_size]
        target_items.unsqueeze(0),     # [1, batch_size, hidden_size]
        dim=2
    ) / temperature  # [batch_size, batch_size]
    
    # 正样本：生成物与其对应真实目标的相似度
    positives = torch.diag(similarities)
    
    # 负样本：生成物与其他用户真实目标的相似度
    mask = torch.eye(batch_size).bool()
    negatives = similarities[~mask].view(batch_size, -1)
    
    # 构建logits并计算交叉熵损失
    logits = torch.cat([positives.unsqueeze(1), negatives], dim=1)
    labels = torch.zeros(batch_size, dtype=torch.long)
    loss = F.cross_entropy(logits, labels)
    
    return loss

def demo_cdm_comparison():
    """演示两种CDM方法的差异"""
    print("=== 目标感知CDM vs 原始CDM 对比演示 ===\n")
    
    batch_size = 4
    hidden_size = 64
    
    # 创建测试数据
    generated_items = torch.randn(batch_size, hidden_size)
    
    print("场景1：用户有相似的目标物品")
    print("-" * 40)
    
    # 场景1：相似的目标物品
    base_target = torch.randn(1, hidden_size)
    similar_targets = base_target + 0.1 * torch.randn(batch_size, hidden_size)
    
    original_loss_1 = original_cdm_loss(generated_items)
    target_aware_loss_1 = target_aware_cdm_loss(generated_items, similar_targets)
    
    print(f"原始CDM损失: {original_loss_1.item():.4f}")
    print(f"目标感知CDM损失: {target_aware_loss_1.item():.4f}")
    print(f"差异: {(original_loss_1 - target_aware_loss_1).item():.4f}")
    
    print("\n场景2：用户有完全不同的目标物品")
    print("-" * 40)
    
    # 场景2：完全不同的目标物品
    different_targets = torch.randn(batch_size, hidden_size)
    
    original_loss_2 = original_cdm_loss(generated_items)
    target_aware_loss_2 = target_aware_cdm_loss(generated_items, different_targets)
    
    print(f"原始CDM损失: {original_loss_2.item():.4f}")
    print(f"目标感知CDM损失: {target_aware_loss_2.item():.4f}")
    print(f"差异: {(original_loss_2 - target_aware_loss_2).item():.4f}")
    
    print("\n场景3：用户有完全相同的目标物品")
    print("-" * 40)
    
    # 场景3：完全相同的目标物品
    same_target = torch.randn(1, hidden_size)
    identical_targets = same_target.repeat(batch_size, 1)
    
    original_loss_3 = original_cdm_loss(generated_items)
    target_aware_loss_3 = target_aware_cdm_loss(generated_items, identical_targets)
    
    print(f"原始CDM损失: {original_loss_3.item():.4f}")
    print(f"目标感知CDM损失: {target_aware_loss_3.item():.4f}")
    print(f"差异: {(original_loss_3 - target_aware_loss_3).item():.4f}")
    
    print("\n=== 分析 ===")
    print("1. 当用户目标相似时，目标感知CDM应该更宽容（损失可能更小）")
    print("2. 当用户目标完全相同时，目标感知CDM的负样本相似度会很高，")
    print("   这使得模型更难区分，可能导致损失增加，这是合理的")
    print("3. 目标感知CDM能够根据真实目标的相似性调整惩罚强度")

def demo_gradient_flow():
    """演示梯度流向的差异"""
    print("\n=== 梯度流向演示 ===\n")
    
    batch_size = 3
    hidden_size = 32
    
    # 创建需要梯度的生成物
    generated_items = torch.randn(batch_size, hidden_size, requires_grad=True)
    target_items = torch.randn(batch_size, hidden_size)
    
    # 计算目标感知CDM损失
    loss = target_aware_cdm_loss(generated_items, target_items)
    loss.backward()
    
    print(f"损失值: {loss.item():.4f}")
    print(f"梯度范数: {generated_items.grad.norm().item():.4f}")
    print(f"梯度形状: {generated_items.grad.shape}")
    
    # 检查梯度的分布
    grad_mean = generated_items.grad.mean().item()
    grad_std = generated_items.grad.std().item()
    print(f"梯度均值: {grad_mean:.6f}")
    print(f"梯度标准差: {grad_std:.6f}")
    
    print("\n✓ 梯度计算正常，可以进行反向传播")

if __name__ == "__main__":
    print("开始CDM模块演示...\n")
    
    try:
        demo_cdm_comparison()
        demo_gradient_flow()
        
        print("\n🎉 演示完成！")
        print("\n核心改进总结：")
        print("- 原始CDM：无差别推开所有生成物")
        print("- 目标感知CDM：基于真实目标物品进行有针对性的对比学习")
        print("- 优势：更精确的区分性，缓解对合理相似性的过度惩罚")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
